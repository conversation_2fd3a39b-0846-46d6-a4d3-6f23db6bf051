#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل نتائج فك التشفير واستخراج المعلومات المفيدة
"""

import json
import re
from typing import List, Dict

def analyze_decoded_results():
    """
    تحليل نتائج فك التشفير المحفوظة
    """
    print("🔍 تحليل نتائج فك التشفير...")
    print("=" * 50)
    
    # قراءة النتائج
    try:
        with open('decoded_results.json', 'r', encoding='utf-8') as f:
            results = json.load(f)
    except FileNotFoundError:
        print("❌ ملف النتائج غير موجود. يرجى تشغيل stream_decoder.py أولاً")
        return
    
    print(f"✅ حالة فك التشفير: {'نجح' if results['success'] else 'فشل'}")
    print(f"📊 عدد النصوص المفكوكة: {len(results['decoded_strings'])}")
    print(f"🔗 عدد روابط البث: {len(results['stream_urls'])}")
    
    # تحليل النصوص المفكوكة
    print("\n🔓 تحليل النصوص المفكوكة:")
    print("-" * 30)
    
    important_findings = []
    
    for i, item in enumerate(results['decoded_strings'], 1):
        decrypted = item['decrypted']
        print(f"\n{i}. النص المفكوك:")
        print(f"   الطول: {len(decrypted)} حرف")
        
        # البحث عن كلمات مفتاحية مهمة
        keywords = {
            'constructor': 'دالة البناء',
            'function': 'دالة',
            'var': 'متغير',
            'http': 'رابط HTTP',
            'https': 'رابط HTTPS',
            'm3u8': 'ملف بث مباشر',
            'playlist': 'قائمة تشغيل',
            'stream': 'بث',
            'live': 'مباشر',
            'channel': 'قناة',
            'video': 'فيديو',
            'source': 'مصدر',
            'url': 'رابط',
            'link': 'رابط'
        }
        
        found_keywords = []
        for keyword, description in keywords.items():
            if keyword.lower() in decrypted.lower():
                found_keywords.append(f"{keyword} ({description})")
        
        if found_keywords:
            print(f"   🎯 كلمات مفتاحية: {', '.join(found_keywords)}")
            important_findings.append({
                'text_number': i,
                'keywords': found_keywords,
                'content': decrypted[:200] + '...' if len(decrypted) > 200 else decrypted
            })
        
        # البحث عن أنماط الروابط
        url_patterns = [
            r'https?://[^\s"\'<>]+',
            r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/[^\s"\'<>]+',
            r'[^\s"\'<>]*\.m3u8[^\s"\'<>]*',
            r'[^\s"\'<>]*\.ts[^\s"\'<>]*',
            r'[^\s"\'<>]*playlist[^\s"\'<>]*'
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, decrypted, re.IGNORECASE)
            if matches:
                print(f"   🌐 روابط محتملة: {matches}")
                important_findings.append({
                    'text_number': i,
                    'type': 'potential_urls',
                    'urls': matches
                })
    
    # تحليل خاص للنص الأكثر أهمية
    print("\n🎯 التحليل المتقدم:")
    print("-" * 20)
    
    # النص الذي يحتوي على "constructor"
    constructor_text = None
    for item in results['decoded_strings']:
        if 'constructor' in item['decrypted'].lower():
            constructor_text = item['decrypted']
            break
    
    if constructor_text:
        print("✅ تم العثور على نص يحتوي على 'constructor'")
        print(f"   المحتوى: {constructor_text}")
        
        # هذا النص يحتوي على الأبجدية - قد يكون مفتاح فك التشفير
        if 'abcdefghijklmnopqrstuvwxyz' in constructor_text:
            print("🔑 تم العثور على مفتاح الأبجدية - هذا مهم لفك التشفير!")
    
    # البحث عن دالة JavaScript مفكوكة
    js_function = None
    for item in results['decoded_strings']:
        if 'var f=' in item['decrypted'] and 'function' in item['decrypted']:
            js_function = item['decrypted']
            break
    
    if js_function:
        print("\n🔧 تم العثور على دالة JavaScript مفكوكة:")
        print("   هذه الدالة قد تحتوي على منطق فك التشفير الحقيقي")
        
        # محاولة استخراج معلومات من الدالة
        numbers = re.findall(r'\d+', js_function)
        if numbers:
            print(f"   أرقام مهمة في الدالة: {numbers[:10]}")
    
    # ملخص النتائج
    print("\n📋 ملخص التحليل:")
    print("=" * 20)
    
    if important_findings:
        print("✅ تم العثور على معلومات مهمة:")
        for finding in important_findings:
            if 'keywords' in finding:
                print(f"   - النص {finding['text_number']}: {', '.join(finding['keywords'])}")
            elif 'urls' in finding:
                print(f"   - النص {finding['text_number']}: روابط محتملة")
    else:
        print("⚠️ لم يتم العثور على روابط بث مباشرة")
    
    # توصيات
    print("\n💡 التوصيات:")
    print("-" * 15)
    
    if constructor_text and 'abcdefghijklmnopqrstuvwxyz' in constructor_text:
        print("1. ✅ تم فك التشفير الأساسي بنجاح")
        print("2. 🔑 تم العثور على مفتاح الأبجدية")
        print("3. 📝 الكود يستخدم نظام تشفير معقد")
    
    if js_function:
        print("4. 🔧 تم استخراج دالة JavaScript - قد تحتاج لتشغيلها في متصفح")
    
    print("5. 🌐 للحصول على روابط البث الفعلية:")
    print("   - ضع الكود في صفحة HTML")
    print("   - استخدم أدوات المطور (F12)")
    print("   - راقب طلبات الشبكة")
    print("   - ابحث عن ملفات .m3u8 أو .ts")
    
    # حفظ التحليل
    analysis_result = {
        'summary': {
            'success': results['success'],
            'decoded_strings_count': len(results['decoded_strings']),
            'stream_urls_count': len(results['stream_urls']),
            'has_constructor': constructor_text is not None,
            'has_js_function': js_function is not None
        },
        'important_findings': important_findings,
        'recommendations': [
            "استخدم أدوات المطور في المتصفح",
            "راقب طلبات الشبكة أثناء تشغيل الكود",
            "ابحث عن ملفات .m3u8 في Network tab",
            "جرب تشغيل الكود في بيئة JavaScript"
        ]
    }
    
    with open('analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2)
    
    print("\n💾 تم حفظ تقرير التحليل في ملف analysis_report.json")
    print("\n" + "=" * 50)
    print("انتهى التحليل")

if __name__ == "__main__":
    analyze_decoded_results()
