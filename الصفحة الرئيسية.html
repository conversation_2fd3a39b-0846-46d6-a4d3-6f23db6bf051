<!DOCTYPE html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background-image: url('https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?auto=format&fit=crop&w=1050&q=80');
      background-size: cover;
      background-position: center;
      background-attachment: fixed;
      color: #ffffff;
    }

    .overlay {
      background-color: rgba(0, 0, 0, 0.75);
      padding: 20px;
      min-height: 100vh;
    }

    .title {
      text-align: center;
      color: #0EA691;
      font-size: 26px;
      margin-bottom: 10px;
    }

    .top-buttons {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .top-buttons button {
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      background-color: #0EA691;
      color: #fff;
      font-weight: bold;
      cursor: pointer;
      transition: background 0.3s;
    }

    .top-buttons button:hover {
      background-color: #088e7a;
    }

    .search-box {
      text-align: center;
      margin-bottom: 20px;
    }

    .search-input {
      padding: 10px;
      width: 80%;
      max-width: 300px;
      border-radius: 6px;
      border: none;
      outline: none;
    }

    .section-title {
      color: #0EA691;
      font-size: 18px;
      margin: 30px 10px 10px;
    }

    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 12px;
      padding: 10px;
    }

    .card {
      background-color: #2a2a2a;
      border-radius: 10px;
      box-shadow: 0 0 8px #0ea691;
      overflow: hidden;
      text-align: center;
      transition: transform 0.2s;
    }

    .card:hover {
      transform: scale(1.03);
    }

    .channel-img {
      width: 100%;
      height: 100px;
      object-fit: cover;
      border-bottom: 1px solid #0EA691;
    }

    .channel-name {
      padding: 8px;
      font-size: 14px;
      color: #0EA691;
      font-weight: bold;
    }

    .category-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
      padding: 0 10px;
    }

    .category-card {
      border-radius: 12px;
      overflow: hidden;
      text-align: center;
      transition: transform 0.3s, box-shadow 0.3s;
      text-decoration: none;
      display: block;
    }

    .category-card:hover {
      transform: scale(1.05);
      box-shadow: 0 0 25px rgba(14, 166, 145, 0.8);
    }

    .category-img {
      width: 100%;
      height: 120px;
      object-fit: cover;
    }

    .category-content {
      padding: 12px;
    }

    .category-title {
      color: #fff;
      margin: 8px 0 5px;
      font-size: 16px;
      font-weight: bold;
    }

    .category-desc {
      font-size: 12px;
      margin: 0;
      opacity: 0.9;
    }

    footer {
      text-align: center;
      color: #0EA691;
      margin-top: 40px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="overlay">

    <h2 class="title">📺 راية كافية</h2>

    <div style="text-align: center; margin-bottom: 20px;">
      <p style="color: #0EA691; font-size: 18px; margin: 0;">اختر مجموعة القنوات التي تريد مشاهدتها</p>
    </div>
<!-- Featured Cards for Channel Categories -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-bottom: 30px; padding: 0 10px;">

  <!-- Club World Cup Channels -->
  <a href="قنوات كأس العالم للأندية.html" style="text-decoration: none;">
    <div style="
      background: linear-gradient(135deg, #0ea691, #064f44);
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(14, 166, 145, 0.6);
      overflow: hidden;
      text-align: center;
      transition: transform 0.3s;
    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
      <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSSTqqqaIEr2LG_R3qJPS3-cwyeErjlPa7a2w&s" style="width: 100%; height: 120px; object-fit: cover;">
      <div style="padding: 12px;">
        <h3 style="color: #fff; margin: 8px 0 5px; font-size: 16px;">⚽ قنوات كأس العالم للأندية</h3>
        <p style="color: #c8f7f3; font-size: 12px; margin: 0;">شاهد مباريات البطولة مباشرة</p>
      </div>
    </div>
  </a>

  <!-- beIN Sports Channels -->
  <a href="قنوات beIN Sports.html" style="text-decoration: none;">
    <div style="
      background: linear-gradient(135deg, #e74c3c, #c0392b);
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(231, 76, 60, 0.6);
      overflow: hidden;
      text-align: center;
      transition: transform 0.3s;
    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
      <img src="https://www.bein-tv.cc/uploads/channels/672a6f53057e7.png" style="width: 100%; height: 120px; object-fit: cover;">
      <div style="padding: 12px;">
        <h3 style="color: #fff; margin: 8px 0 5px; font-size: 16px;">📺 قنوات beIN Sports</h3>
        <p style="color: #ffcccb; font-size: 12px; margin: 0;">جميع قنوات بي إن سبورت</p>
      </div>
    </div>
  </a>

  <!-- ON Time Sports Channels -->
  <a href="قنوات ON Time Sports.html" style="text-decoration: none;">
    <div style="
      background: linear-gradient(135deg, #3498db, #2980b9);
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(52, 152, 219, 0.6);
      overflow: hidden;
      text-align: center;
      transition: transform 0.3s;
    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
      <img src="https://www.bein-tv.cc/uploads/channels/67458f181563d.jpg" style="width: 100%; height: 120px; object-fit: cover;">
      <div style="padding: 12px;">
        <h3 style="color: #fff; margin: 8px 0 5px; font-size: 16px;">📺 قنوات ON Time Sports</h3>
        <p style="color: #cce7ff; font-size: 12px; margin: 0;">جميع قنوات أون تايم سبورت</p>
      </div>
    </div>
  </a>

  <!-- SSC Sports Channels -->
  <a href="قنوات SSC Sports.html" style="text-decoration: none;">
    <div style="
      background: linear-gradient(135deg, #9b59b6, #8e44ad);
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(155, 89, 182, 0.6);
      overflow: hidden;
      text-align: center;
      transition: transform 0.3s;
    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
      <img src="https://www.bein-tv.cc/uploads/channels/672b6a5cb4b54.png" style="width: 100%; height: 120px; object-fit: cover;">
      <div style="padding: 12px;">
        <h3 style="color: #fff; margin: 8px 0 5px; font-size: 16px;">📺 قنوات SSC Sports</h3>
        <p style="color: #e8d5f0; font-size: 12px; margin: 0;">جميع قنوات السعودية الرياضية</p>
      </div>
    </div>
  </a>

  <!-- Club Channels -->
  <a href="قنوات الأندية.html" style="text-decoration: none;">
    <div style="
      background: linear-gradient(135deg, #f39c12, #e67e22);
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(243, 156, 18, 0.6);
      overflow: hidden;
      text-align: center;
      transition: transform 0.3s;
    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
      <img src="https://yt3.googleusercontent.com/ytc/AIdro_mGOi86457H-pnYwuUyu2OSWpdvfcmkZBqwJ8Ctfss2lUg=s900-c-k-c0x00ffffff-no-rj" style="width: 100%; height: 120px; object-fit: cover;">
      <div style="padding: 12px;">
        <h3 style="color: #fff; margin: 8px 0 5px; font-size: 16px;">⚽ قنوات الأندية</h3>
        <p style="color: #fdeaa7; font-size: 12px; margin: 0;">قنوات الأندية العالمية والمحلية</p>
      </div>
    </div>
  </a>

  <!-- Quick Access Section -->
  <a href="جدول المباريات.html" style="text-decoration: none;">
    <div style="
      background: linear-gradient(135deg, #27ae60, #229954);
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(39, 174, 96, 0.6);
      overflow: hidden;
      text-align: center;
      transition: transform 0.3s;
    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
      <img src="https://images.unsplash.com/photo-1574629810360-7efbbe195018?auto=format&fit=crop&w=1050&q=80" style="width: 100%; height: 120px; object-fit: cover;">
      <div style="padding: 12px;">
        <h3 style="color: #fff; margin: 8px 0 5px; font-size: 16px;">📅 جدول المباريات</h3>
        <p style="color: #d5f4e6; font-size: 12px; margin: 0;">مواعيد المباريات اليومية</p>
      </div>
    </div>
  </a>

</div>

       
    <footer>
      <p><strong>جميع الحقوق محفوظة © Rayaa Cafe <script>document.write(new Date().getFullYear());</script></strong></p>
    </footer>
  </div>

  <script>
    function downloadPlayer() {
      window.open("https://play.google.com/store/apps/details?id=com.ytv.pronew", "_blank");
    }

    function shareApp() {
      const shareText = "📺 جرّب الآن تطبيق راية كافية!\n\nحمل التطبيق من هنا:\nhttps://your_app_link.com";
      if (navigator.share) {
        navigator.share({
          title: "مجمع القنوات الرياضية",
          text: shareText,
          url: "https://your_app_link.com"
        });
      } else {
        alert("انسخ هذا الرابط وشاركه:\n" + shareText);
      }
    }

    // Add smooth hover effects
    document.addEventListener('DOMContentLoaded', function() {
      const categoryCards = document.querySelectorAll('[style*="background: linear-gradient"]');
      categoryCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'scale(1.05)';
          this.style.boxShadow = '0 0 25px rgba(14, 166, 145, 0.8)';
        });

        card.addEventListener('mouseleave', function() {
          this.style.transform = 'scale(1)';
          this.style.boxShadow = this.style.boxShadow.replace('25px', '15px');
        });
      });
    });
  </script>
</body>
</html>
