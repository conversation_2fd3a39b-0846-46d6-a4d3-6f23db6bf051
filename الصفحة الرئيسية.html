<!DOCTYPE html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background-image: url('https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?auto=format&fit=crop&w=1050&q=80');
      background-size: cover;
      background-position: center;
      background-attachment: fixed;
      color: #ffffff;
    }

    .overlay {
      background-color: rgba(0, 0, 0, 0.75);
      padding: 20px;
      min-height: 100vh;
    }

    .title {
      text-align: center;
      color: #0EA691;
      font-size: 26px;
      margin-bottom: 10px;
    }

    .top-buttons {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .top-buttons button {
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      background-color: #0EA691;
      color: #fff;
      font-weight: bold;
      cursor: pointer;
      transition: background 0.3s;
    }

    .top-buttons button:hover {
      background-color: #088e7a;
    }

    .search-box {
      text-align: center;
      margin-bottom: 20px;
    }

    .search-input {
      padding: 10px;
      width: 80%;
      max-width: 300px;
      border-radius: 6px;
      border: none;
      outline: none;
    }

    .section-title {
      color: #0EA691;
      font-size: 18px;
      margin: 30px 10px 10px;
    }

    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 12px;
      padding: 10px;
    }

    .card {
      background-color: #2a2a2a;
      border-radius: 10px;
      box-shadow: 0 0 8px #0ea691;
      overflow: hidden;
      text-align: center;
      transition: transform 0.2s;
    }

    .card:hover {
      transform: scale(1.03);
    }

    .channel-img {
      width: 100%;
      height: 100px;
      object-fit: cover;
      border-bottom: 1px solid #0EA691;
    }

    .channel-name {
      padding: 8px;
      font-size: 14px;
      color: #0EA691;
      font-weight: bold;
    }

    footer {
      text-align: center;
      color: #0EA691;
      margin-top: 40px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="overlay">

    <h2 class="title">📺 راية كافية</h2>

    <div class="search-box">
      <input type="text" id="search" class="search-input" placeholder="ابحث عن قناة...">
    </div>
<!-- Featured Card for Club World Cup Channels -->
<div style="display: flex; justify-content: center; margin-bottom: 20px;">
  <a href="go:clubworld" style="text-decoration: none;">
    <div style="
      width: 300px;
      background: linear-gradient(135deg, #0ea691, #064f44);
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(14, 166, 145, 0.6);
      overflow: hidden;
      text-align: center;
      transition: transform 0.3s;
    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
      <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSSTqqqaIEr2LG_R3qJPS3-cwyeErjlPa7a2w&s" style="width: 100%; height: 150px; object-fit: cover;">
      <div style="padding: 12px;">
        <h3 style="color: #fff; margin: 10px 0 5px;">قنوات كأس العالم للأندية</h3>
        <p style="color: #c8f7f3; font-size: 14px;">شاهد مباريات البطولة مباشرة عبر تطبيقنا</p>
      </div>
    </div>
  </a>
</div>



    <!-- beIN Section -->
    <h3 class="section-title">قنوات beIN Sports</h3>
    <div class="grid" id="bein">
      <a href="go:bein1" class="card"><img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/672a6f53057e7.png"><div class="channel-name">beIN Sports 1</div></a>
      <a href="go:bein2" class="card"><img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/672a6f53057e7.png"><div class="channel-name">beIN Sports 2</div></a>
      <a href="go:bein3" class="card"><img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/672a6f53057e7.png"><div class="channel-name">beIN Sports 3</div></a>
    </div>

    <!-- ON Time Section -->
    <h3 class="section-title">قنوات ON Time Sports</h3>
    <div class="grid" id="on">
      <a href="go:on1" class="card"><img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/67458f181563d.jpg"><div class="channel-name">ON Time Sports 1</div></a>
      <a href="go:on2" class="card"><img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/67458f181563d.jpg"><div class="channel-name">ON Time Sports 2</div></a>
    </div>

    <!-- SSC Section -->
    <h3 class="section-title">قنوات SSC Sports</h3>
    <div class="grid" id="ssc">
      <a href="go:ssc1" class="card"><img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/672b6a5cb4b54.png"><div class="channel-name">SSC Sports 1</div></a>
      <a href="go:ssc2" class="card"><img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/672b6a5cb4b54.png"><div class="channel-name">SSC Sports 2</div></a>
    </div>
<!-- Club Channels Section -->
<h3 class="section-title">قنوات الأندية</h3>
<div class="grid" id="clubs">
  <!-- Al Ahly TV -->
  <a href="go:A7A" class="card">
    <img class="channel-img" src="https://yt3.googleusercontent.com/ytc/AIdro_mGOi86457H-pnYwuUyu2OSWpdvfcmkZBqwJ8Ctfss2lUg=s900-c-k-c0x00ffffff-no-rj">
    <div class="channel-name">Al Ahly TV</div>
  </a>

  <!-- Zamalek TV -->
  <a href="go:ZSC" class="card">
    <img class="channel-img" src="https://upload.wikimedia.org/wikipedia/ar/8/8b/%D9%82%D9%86%D8%A7%D8%A9_%D9%86%D8%A7%D8%AF%D9%8A_%D8%A7%D9%84%D8%B2%D9%85%D8%A7%D9%84%D9%83.jpg">
    <div class="channel-name">Zamalek TV</div>
  </a>
</div>

       
    <footer>
      <p><strong>جميع الحقوق محفوظة © Rayaa Cafe <script>document.write(new Date().getFullYear());</script></strong></p>
    </footer>
  </div>

  <script>
    const searchInput = document.getElementById('search');
    searchInput.addEventListener('input', () => {
      const query = searchInput.value.toLowerCase();
      const cards = document.querySelectorAll('.card');
      cards.forEach(card => {
        const name = card.querySelector('.channel-name').textContent.toLowerCase();
        card.style.display = name.includes(query) ? 'block' : 'none';
      });
    });

    function downloadPlayer() {
      window.open("https://play.google.com/store/apps/details?id=com.ytv.pronew", "_blank");
    }

    function shareApp() {
      const shareText = "📺 جرّب الآن تطبيق راية كافية!\n\nحمل التطبيق من هنا:\nhttps://your_app_link.com";
      if (navigator.share) {
        navigator.share({
          title: "مجمع القنوات الرياضية",
          text: shareText,
          url: "https://your_app_link.com"
        });
      } else {
        alert("انسخ هذا الرابط وشاركه:\n" + shareText);
      }
    }
  </script>
</body>
</html>
