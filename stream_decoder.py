#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Stream Decoder - أداة فك تشفير روابط البث المباشر
مطور خصيصاً لفك تشفير الأكواد المعقدة واستخراج روابط البث الحقيقية
"""

import re
import json
import base64
import urllib.parse
import requests
from typing import List, Dict, Optional
import logging

# إعداد نظام التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StreamDecoder:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def dAD_decrypt(self, x: str) -> str:
        """
        دالة فك التشفير المستخرجة من الكود الأصلي
        """
        try:
            h = 6257401
            y = len(x)
            w = list(x)
            
            for u in range(y):
                q = h * (u + 445) + (h % 19574)
                g = h * (u + 267) + (h % 16197)
                o = q % y
                f = g % y
                w[o], w[f] = w[f], w[o]
                h = (q + g) % 7395696
                
            return ''.join(w)
        except Exception as e:
            logger.error(f"خطأ في فك التشفير: {e}")
            return ""
    
    def extract_encrypted_strings(self, code: str) -> List[str]:
        """
        استخراج النصوص المشفرة من الكود
        """
        patterns = [
            r"'([^']{30,})'",  # نصوص طويلة بين علامات اقتباس
            r'"([^"]{30,})"',  # نصوص طويلة بين علامات اقتباس مزدوجة
            r"dAD\('([^']+)'\)",  # نصوص داخل دالة dAD
        ]
        
        encrypted_strings = []
        for pattern in patterns:
            matches = re.findall(pattern, code)
            encrypted_strings.extend(matches)
        
        return list(set(encrypted_strings))  # إزالة المكررات
    
    def find_urls(self, text: str) -> List[str]:
        """
        البحث عن الروابط في النص
        """
        url_patterns = [
            r'https?://[^\s"\'<>]+\.m3u8[^\s"\'<>]*',  # روابط m3u8
            r'https?://[^\s"\'<>]+\.ts[^\s"\'<>]*',    # روابط ts
            r'https?://[^\s"\'<>]+/playlist[^\s"\'<>]*', # روابط playlist
            r'https?://[^\s"\'<>]+/live[^\s"\'<>]*',   # روابط live
            r'https?://[^\s"\'<>]+/stream[^\s"\'<>]*', # روابط stream
            r'https?://[^\s"\'<>]+\.mpd[^\s"\'<>]*',   # روابط DASH
        ]
        
        urls = []
        for pattern in url_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            urls.extend(matches)
        
        return list(set(urls))
    
    def decode_base64_variants(self, text: str) -> List[str]:
        """
        محاولة فك تشفير Base64 بأشكال مختلفة
        """
        decoded_texts = []
        
        # Base64 عادي
        try:
            decoded = base64.b64decode(text).decode('utf-8')
            decoded_texts.append(decoded)
        except:
            pass
        
        # Base64 مع padding
        for padding in ['', '=', '==', '===']:
            try:
                decoded = base64.b64decode(text + padding).decode('utf-8')
                decoded_texts.append(decoded)
            except:
                pass
        
        # URL decode ثم Base64
        try:
            url_decoded = urllib.parse.unquote(text)
            decoded = base64.b64decode(url_decoded).decode('utf-8')
            decoded_texts.append(decoded)
        except:
            pass
        
        return decoded_texts
    
    def analyze_javascript_execution(self, code: str) -> Dict:
        """
        تحليل تنفيذ الكود JavaScript واستخراج المعلومات
        """
        results = {
            'variables': [],
            'function_calls': [],
            'potential_urls': [],
            'decoded_strings': []
        }
        
        # استخراج أسماء المتغيرات
        var_pattern = r'var\s+(\w+)'
        results['variables'] = re.findall(var_pattern, code)
        
        # استخراج استدعاءات الدوال
        func_pattern = r'(\w+)\([^)]*\)'
        results['function_calls'] = re.findall(func_pattern, code)
        
        # البحث عن أرقام مهمة (قد تكون مفاتيح)
        number_pattern = r'\b\d{4,}\b'
        numbers = re.findall(number_pattern, code)
        
        logger.info(f"تم العثور على {len(results['variables'])} متغير")
        logger.info(f"تم العثور على {len(results['function_calls'])} استدعاء دالة")
        logger.info(f"أرقام مهمة: {numbers[:10]}")  # أول 10 أرقام
        
        return results
    
    def validate_stream_url(self, url: str) -> bool:
        """
        التحقق من صحة رابط البث
        """
        try:
            response = self.session.head(url, timeout=10)
            content_type = response.headers.get('content-type', '').lower()
            
            valid_types = [
                'application/vnd.apple.mpegurl',  # m3u8
                'application/x-mpegurl',
                'video/mp2t',  # ts
                'application/dash+xml',  # mpd
                'video/mp4'
            ]
            
            return any(vtype in content_type for vtype in valid_types) or response.status_code == 200
        except:
            return False
    
    def decode_stream(self, encrypted_code: str) -> Dict:
        """
        الدالة الرئيسية لفك تشفير الكود واستخراج روابط البث
        """
        logger.info("🔍 بدء عملية فك التشفير...")
        
        results = {
            'success': False,
            'stream_urls': [],
            'decoded_strings': [],
            'analysis': {},
            'errors': []
        }
        
        try:
            # الخطوة 1: استخراج النصوص المشفرة
            logger.info("📝 استخراج النصوص المشفرة...")
            encrypted_strings = self.extract_encrypted_strings(encrypted_code)
            logger.info(f"تم العثور على {len(encrypted_strings)} نص مشفر")
            
            # الخطوة 2: فك تشفير النصوص
            logger.info("🔓 فك تشفير النصوص...")
            for encrypted in encrypted_strings:
                # فك التشفير باستخدام دالة dAD
                decrypted = self.dAD_decrypt(encrypted)
                if decrypted and decrypted != encrypted:
                    results['decoded_strings'].append({
                        'original': encrypted[:50] + '...' if len(encrypted) > 50 else encrypted,
                        'decrypted': decrypted
                    })
                    
                    # البحث عن روابط في النص المفكوك
                    urls = self.find_urls(decrypted)
                    results['stream_urls'].extend(urls)
                
                # محاولة فك تشفير Base64
                base64_decoded = self.decode_base64_variants(encrypted)
                for decoded in base64_decoded:
                    urls = self.find_urls(decoded)
                    results['stream_urls'].extend(urls)
            
            # الخطوة 3: البحث المباشر عن الروابط في الكود الأصلي
            logger.info("🔍 البحث المباشر عن الروابط...")
            direct_urls = self.find_urls(encrypted_code)
            results['stream_urls'].extend(direct_urls)
            
            # إزالة المكررات
            results['stream_urls'] = list(set(results['stream_urls']))
            
            # الخطوة 4: التحقق من صحة الروابط
            logger.info("✅ التحقق من صحة الروابط...")
            valid_urls = []
            for url in results['stream_urls']:
                logger.info(f"فحص الرابط: {url[:50]}...")
                if self.validate_stream_url(url):
                    valid_urls.append(url)
                    logger.info("✅ رابط صحيح")
                else:
                    logger.info("❌ رابط غير صحيح")
            
            results['stream_urls'] = valid_urls
            
            # الخطوة 5: تحليل الكود
            logger.info("📊 تحليل الكود...")
            results['analysis'] = self.analyze_javascript_execution(encrypted_code)
            
            results['success'] = len(results['stream_urls']) > 0 or len(results['decoded_strings']) > 0
            
        except Exception as e:
            error_msg = f"خطأ في عملية فك التشفير: {str(e)}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
        
        return results

def main():
    """
    الدالة الرئيسية
    """
    print("🔓 Stream Decoder - أداة فك تشفير روابط البث المباشر")
    print("=" * 60)
    
    # قراءة الكود المشفر من الملف
    try:
        with open('Untitled-1.txt', 'r', encoding='utf-8') as f:
            encrypted_code = f.read()
    except FileNotFoundError:
        print("❌ لم يتم العثور على ملف Untitled-1.txt")
        return
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return
    
    # إنشاء كائن فك التشفير
    decoder = StreamDecoder()
    
    # فك التشفير
    results = decoder.decode_stream(encrypted_code)
    
    # عرض النتائج
    print("\n📊 نتائج فك التشفير:")
    print("=" * 40)
    
    if results['success']:
        print("✅ تم فك التشفير بنجاح!")
        
        if results['stream_urls']:
            print(f"\n📺 روابط البث المكتشفة ({len(results['stream_urls'])}):")
            for i, url in enumerate(results['stream_urls'], 1):
                print(f"{i}. {url}")
        
        if results['decoded_strings']:
            print(f"\n🔓 النصوص المفكوكة ({len(results['decoded_strings'])}):")
            for i, item in enumerate(results['decoded_strings'], 1):
                print(f"{i}. الأصلي: {item['original']}")
                print(f"   المفكوك: {item['decrypted'][:100]}...")
                print()
        
        # حفظ النتائج في ملف JSON
        with open('decoded_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print("💾 تم حفظ النتائج في ملف decoded_results.json")
        
    else:
        print("❌ لم يتم العثور على روابط بث صحيحة")
        
        if results['errors']:
            print("\n🚨 الأخطاء:")
            for error in results['errors']:
                print(f"- {error}")
    
    print("\n" + "=" * 60)
    print("انتهت عملية فك التشفير")

if __name__ == "__main__":
    main()
