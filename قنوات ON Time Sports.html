<!DOCTYPE html>
<head>
  <meta charset="UTF-8">
  <title>قنوات ON Time Sports</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background-image: url('https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?auto=format&fit=crop&w=1050&q=80');
      background-size: cover;
      background-position: center;
      background-attachment: fixed;
      color: #ffffff;
    }

    .overlay {
      background-color: rgba(0, 0, 0, 0.75);
      padding: 20px;
      min-height: 100vh;
    }

    .title {
      text-align: center;
      color: #0EA691;
      font-size: 26px;
      margin-bottom: 10px;
    }

    .search-box {
      text-align: center;
      margin-bottom: 20px;
    }

    .search-input {
      padding: 10px;
      width: 80%;
      max-width: 300px;
      border-radius: 6px;
      border: none;
      outline: none;
    }

    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 12px;
      padding: 10px;
    }

    .card {
      background-color: #2a2a2a;
      border-radius: 10px;
      box-shadow: 0 0 8px #0ea691;
      overflow: hidden;
      text-align: center;
      transition: transform 0.2s;
    }

    .card:hover {
      transform: scale(1.03);
    }

    .channel-img {
      width: 100%;
      height: 100px;
      object-fit: cover;
      border-bottom: 1px solid #0EA691;
    }

    .channel-name {
      padding: 8px;
      font-size: 14px;
      color: #0EA691;
      font-weight: bold;
    }

    .back-btn {
      display: block;
      text-align: center;
      margin-top: 20px;
    }

    .back-btn button {
      padding: 10px 20px;
      border: none;
      background-color: #0EA691;
      color: white;
      border-radius: 6px;
      font-weight: bold;
      cursor: pointer;
    }

    .back-btn button:hover {
      background-color: #088e7a;
    }

    footer {
      text-align: center;
      color: #0EA691;
      margin-top: 40px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="overlay">
    <h2 class="title">📺 قنوات ON Time Sports</h2>

    <div class="search-box">
      <input type="text" id="search" class="search-input" placeholder="ابحث عن قناة...">
    </div>

    <div class="grid">
      <a href="go:on1" class="card">
        <img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/67458f181563d.jpg">
        <div class="channel-name">ON Time Sports 1</div>
      </a>

      <a href="go:on2" class="card">
        <img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/67458f181563d.jpg">
        <div class="channel-name">ON Time Sports 2</div>
      </a>

      <a href="go:on3" class="card">
        <img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/67458f181563d.jpg">
        <div class="channel-name">ON Time Sports 3</div>
      </a>

      <a href="go:onlive1" class="card">
        <img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/67458f181563d.jpg">
        <div class="channel-name">ON Time Live 1</div>
      </a>

      <a href="go:onlive2" class="card">
        <img class="channel-img" src="https://www.bein-tv.cc/uploads/channels/67458f181563d.jpg">
        <div class="channel-name">ON Time Live 2</div>
      </a>
    </div>

    <div class="back-btn">
      <button onclick="goHome()">🔙 العودة للصفحة الرئيسية</button>
    </div>

    <footer>
      <p><strong>جميع الحقوق محفوظة © Rayaa Cafe <script>document.write(new Date().getFullYear());</script></strong></p>
    </footer>
  </div>

  <script>
    const searchInput = document.getElementById('search');
    searchInput.addEventListener('input', () => {
      const query = searchInput.value.toLowerCase();
      const cards = document.querySelectorAll('.card');
      cards.forEach(card => {
        const name = card.querySelector('.channel-name').textContent.toLowerCase();
        card.style.display = name.includes(query) ? 'block' : 'none';
      });
    });

    function goHome() {
      window.location.href = "الصفحة الرئيسية.html";
    }
  </script>
</body>
</html>
