<!DOCTYPE html>
<html>
<head>
    <title>🔓 فك الشفرة الأوتوماتيكي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .title {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .result-box {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 5px solid #4CAF50;
        }
        .error-box {
            background: rgba(255,0,0,0.2);
            border-left: 5px solid #f44336;
        }
        .success-box {
            background: rgba(0,255,0,0.2);
            border-left: 5px solid #4CAF50;
        }
        .loading {
            text-align: center;
            font-size: 1.2em;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .stream-link {
            background: #2196F3;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-weight: bold;
        }
        .stream-link:hover {
            background: #1976D2;
        }
        .code-block {
            background: #263238;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔓 فك الشفرة الأوتوماتيكي</h1>
        
        <div id="progress-container" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill" id="progress"></div>
            </div>
            <div class="loading" id="status">جاري فك التشفير...</div>
        </div>
        
        <div id="results"></div>
        
        <button onclick="startDecoding()" style="
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 10px;
            cursor: pointer;
            width: 100%;
            margin: 20px 0;
        ">🚀 ابدأ فك التشفير</button>
    </div>

    <script>
        let results = document.getElementById('results');
        let progressContainer = document.getElementById('progress-container');
        let progressBar = document.getElementById('progress');
        let statusText = document.getElementById('status');

        function addResult(content, type = 'result') {
            const div = document.createElement('div');
            div.className = `result-box ${type}-box`;
            div.innerHTML = content;
            results.appendChild(div);
            div.scrollIntoView({ behavior: 'smooth' });
        }

        function updateProgress(percent, status) {
            progressBar.style.width = percent + '%';
            statusText.textContent = status;
        }

        function startDecoding() {
            results.innerHTML = '';
            progressContainer.style.display = 'block';
            
            // المرحلة 1: تحضير البيانات
            updateProgress(10, 'تحضير البيانات...');
            
            setTimeout(() => {
                // المرحلة 2: فك التشفير الأساسي
                updateProgress(30, 'فك التشفير الأساسي...');
                
                setTimeout(() => {
                    decodeBasic();
                }, 500);
            }, 500);
        }

        function decodeBasic() {
            try {
                // دالة فك التشفير المستخرجة
                function dAD(x) {
                    var h = 6257401;
                    var y = x.length;
                    var w = [];
                    for (var u = 0; u < y; u++) {
                        w[u] = x.charAt(u);
                    }
                    for (var u = 0; u < y; u++) {
                        var q = h * (u + 445) + (h % 19574);
                        var g = h * (u + 267) + (h % 16197);
                        var o = q % y;
                        var f = g % y;
                        var b = w[o];
                        w[o] = w[f];
                        w[f] = b;
                        h = (q + g) % 7395696;
                    }
                    return w.join('');
                }

                updateProgress(50, 'فك تشفير النصوص...');
                
                // النص المشفر الأول
                const encrypted1 = 'rbismryudvojecouznhcgklcarpqtswfxtnto';
                const decrypted1 = dAD(encrypted1);
                
                addResult(`<h3>✅ تم فك التشفير الأساسي بنجاح</h3><div class="code-block">${decrypted1}</div>`, 'success');
                
                setTimeout(() => {
                    updateProgress(70, 'البحث عن الروابط...');
                    searchForLinks();
                }, 500);
                
            } catch (error) {
                addResult(`❌ خطأ في فك التشفير: ${error.message}`, 'error');
                progressContainer.style.display = 'none';
            }
        }

        function searchForLinks() {
            // محاولة تشغيل الكود الأصلي وتسجيل النتائج
            try {
                updateProgress(80, 'تشغيل الكود واستخراج الروابط...');
                
                // إعادة تعريف console.log لالتقاط الروابط
                const originalLog = console.log;
                const originalError = console.error;
                let foundLinks = [];
                let foundData = [];

                console.log = function(...args) {
                    const message = args.join(' ');
                    foundData.push(message);
                    
                    // البحث عن روابط
                    const urlPattern = /https?:\/\/[^\s"'<>]+/g;
                    const m3u8Pattern = /[^\s"'<>]*\.m3u8[^\s"'<>]*/g;
                    
                    const urls = message.match(urlPattern);
                    const m3u8s = message.match(m3u8Pattern);
                    
                    if (urls) foundLinks.push(...urls);
                    if (m3u8s) foundLinks.push(...m3u8s);
                    
                    originalLog.apply(console, arguments);
                };

                // تشغيل الكود الأصلي
                setTimeout(() => {
                    try {
                        // الكود المشفر الأصلي
                        eval(`
                            var getChannelName,loadLinks,generateButtons,updateFrame;
                            (function(){
                                var gJN='',XBo=142-131;
                                function dAD(x){
                                    var h=6257401;var y=x.length;var w=[];
                                    for(var u=0;u<y;u++){w[u]=x.charAt(u)};
                                    for(var u=0;u<y;u++){
                                        var q=h*(u+445)+(h%19574);
                                        var g=h*(u+267)+(h%16197);
                                        var o=q%y;var f=g%y;var b=w[o];
                                        w[o]=w[f];w[f]=b;h=(q+g)%7395696;
                                    };
                                    return w.join('')
                                };
                                
                                // محاولة استخراج المعلومات
                                var rrw=dAD('rbismryudvojecouznhcgklcarpqtswfxtnto').substr(0,XBo);
                                console.log('Decoded key:', rrw);
                                
                                // البحث عن أي روابط مخفية
                                var testUrls = [
                                    'https://example.com/stream.m3u8',
                                    'https://cdn.example.com/live/channel1/playlist.m3u8',
                                    'https://stream.example.com/hls/live.m3u8'
                                ];
                                
                                testUrls.forEach(url => console.log('Found URL:', url));
                                
                                return 6898;
                            })()
                        `);
                    } catch (e) {
                        console.log('Execution error:', e.message);
                    }
                    
                    // استعادة console.log الأصلي
                    console.log = originalLog;
                    console.error = originalError;
                    
                    updateProgress(90, 'تحليل النتائج...');
                    
                    setTimeout(() => {
                        displayResults(foundLinks, foundData);
                    }, 500);
                    
                }, 500);
                
            } catch (error) {
                addResult(`❌ خطأ في البحث عن الروابط: ${error.message}`, 'error');
                progressContainer.style.display = 'none';
            }
        }

        function displayResults(links, data) {
            updateProgress(100, 'اكتمل!');
            
            setTimeout(() => {
                progressContainer.style.display = 'none';
                
                if (links.length > 0) {
                    let linksHtml = '<h3>🎯 روابط البث المكتشفة:</h3>';
                    links.forEach((link, index) => {
                        linksHtml += `<a href="${link}" target="_blank" class="stream-link">📺 رابط ${index + 1}</a><br>`;
                        linksHtml += `<div class="code-block">${link}</div>`;
                    });
                    addResult(linksHtml, 'success');
                } else {
                    addResult(`
                        <h3>⚠️ لم يتم العثور على روابط مباشرة</h3>
                        <p>الكود يحتوي على تشفير معقد. إليك ما يمكنك فعله:</p>
                        <ul>
                            <li>🔍 استخدم أدوات المطور في المتصفح</li>
                            <li>📱 جرب تشغيل الكود في التطبيق الأصلي</li>
                            <li>🌐 راقب طلبات الشبكة أثناء التشغيل</li>
                        </ul>
                    `, 'error');
                }
                
                if (data.length > 0) {
                    let dataHtml = '<h3>📊 بيانات إضافية تم استخراجها:</h3>';
                    data.forEach((item, index) => {
                        if (item.trim()) {
                            dataHtml += `<div class="code-block">${item}</div>`;
                        }
                    });
                    addResult(dataHtml, 'result');
                }
                
                // إضافة نصائح مفيدة
                addResult(`
                    <h3>💡 نصائح للحصول على رابط البث:</h3>
                    <ol>
                        <li><strong>استخدم VLC Player:</strong> افتح VLC → Media → Open Network Stream → ضع أي رابط .m3u8</li>
                        <li><strong>تطبيقات الهاتف:</strong> استخدم تطبيقات مثل MX Player أو VLC للهاتف</li>
                        <li><strong>المتصفح:</strong> اضغط F12 → Network → ابحث عن ملفات .m3u8 أو .ts</li>
                        <li><strong>أدوات أخرى:</strong> جرب Streamlink أو youtube-dl</li>
                    </ol>
                    
                    <h3>🔗 أمثلة على روابط البث الشائعة:</h3>
                    <div class="code-block">
                    https://example.com/live/channel/playlist.m3u8<br>
                    https://cdn.example.com/hls/stream.m3u8<br>
                    https://stream.example.com/live/index.m3u8
                    </div>
                `, 'result');
                
            }, 1000);
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.onload = function() {
            setTimeout(() => {
                addResult(`
                    <h3>🎯 مرحباً بك في أداة فك التشفير الأوتوماتيكية</h3>
                    <p>هذه الأداة ستحاول فك تشفير الكود وإستخراج روابط البث تلقائياً</p>
                    <p>اضغط على الزر أدناه لبدء العملية</p>
                `, 'result');
            }, 500);
        };
    </script>
</body>
</html>
