#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
استخراج رابط البث المباشر من الكود المشفر
"""

import re
import base64
import urllib.parse
import json

def dAD_decrypt(x):
    """دالة فك التشفير المستخرجة من الكود الأصلي"""
    h = 6257401
    y = len(x)
    w = list(x)
    
    for u in range(y):
        q = h * (u + 445) + (h % 19574)
        g = h * (u + 267) + (h % 16197)
        o = q % y
        f = g % y
        w[o], w[f] = w[f], w[o]
        h = (q + g) % 7395696
    
    return ''.join(w)

def advanced_js_decoder(encoded_text):
    """
    فك تشفير JavaScript المتقدم باستخدام الخوارزمية المستخرجة
    """
    try:
        # الخوارزمية المستخرجة من النص المفكوك
        f = 12 + 21  # 33
        d = 38 + 55  # 93  
        c = 12 + 84  # 96
        
        e = "abcdefghijklmnopqrstuvwxyz"
        l = [88,70,86,89,90,72,60,87,85,65,94,74,80,75,79,76,66,81,71,82]
        
        # إنشاء جدول الترجمة
        b = {}
        for a in range(len(l)):
            b[l[a]] = a + 1
        
        # معالجة النص
        result = []
        i = 0
        while i < len(encoded_text):
            char = encoded_text[i]
            char_code = ord(char)
            
            if char_code in b:
                # فك التشفير باستخدام الجدول
                p = b[char_code]
                if i + 1 < len(encoded_text):
                    q = (p - 1) * d + ord(encoded_text[i + 1]) - f
                    if q >= 0 and q < len(encoded_text):
                        result.append(encoded_text[q] if q < len(encoded_text) else char)
                    i += 2
                else:
                    result.append(char)
                    i += 1
            elif char_code == c:
                # معالجة خاصة للرمز c
                if i + 2 < len(encoded_text):
                    q = d * (len(l) - f + ord(encoded_text[i + 1])) + ord(encoded_text[i + 2]) - f
                    if q >= 0 and q < len(encoded_text):
                        result.append(encoded_text[q] if q < len(encoded_text) else char)
                    i += 3
                else:
                    result.append(char)
                    i += 1
            else:
                result.append(char)
                i += 1
        
        return ''.join(result)
    except:
        return encoded_text

def extract_stream_urls():
    """استخراج روابط البث من الكود"""
    
    print("🔍 بحث عن روابط البث في الكود المشفر...")
    print("=" * 50)
    
    # قراءة الكود المشفر
    try:
        with open('Untitled-1.txt', 'r', encoding='utf-8') as f:
            code = f.read()
    except FileNotFoundError:
        print("❌ ملف Untitled-1.txt غير موجود")
        return
    
    # استخراج النصوص المشفرة
    encrypted_patterns = [
        r"dAD\('([^']+)'\)",
        r"'([^']{50,})'",
        r'"([^"]{50,})"'
    ]
    
    all_encrypted = []
    for pattern in encrypted_patterns:
        matches = re.findall(pattern, code)
        all_encrypted.extend(matches)
    
    print(f"📝 تم العثور على {len(all_encrypted)} نص مشفر")
    
    # فك التشفير والبحث عن الروابط
    found_urls = []
    decoded_texts = []
    
    for i, encrypted in enumerate(all_encrypted):
        print(f"\n🔓 فك تشفير النص {i+1}...")
        
        # فك التشفير الأساسي
        decrypted1 = dAD_decrypt(encrypted)
        decoded_texts.append(decrypted1)
        
        # فك التشفير المتقدم
        decrypted2 = advanced_js_decoder(encrypted)
        if decrypted2 != encrypted:
            decoded_texts.append(decrypted2)
        
        # البحث عن الروابط في النصوص المفكوكة
        for text in [decrypted1, decrypted2]:
            # أنماط البحث عن الروابط
            url_patterns = [
                r'https?://[^\s"\'<>)]+\.m3u8[^\s"\'<>)]*',
                r'https?://[^\s"\'<>)]+\.ts[^\s"\'<>)]*',
                r'https?://[^\s"\'<>)]+/playlist[^\s"\'<>)]*',
                r'https?://[^\s"\'<>)]+/live[^\s"\'<>)]*',
                r'https?://[^\s"\'<>)]+/stream[^\s"\'<>)]*',
                r'https?://[^\s"\'<>)]+\.mpd[^\s"\'<>)]*',
                r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/[^\s"\'<>)]*\.m3u8[^\s"\'<>)]*'
            ]
            
            for pattern in url_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                found_urls.extend(matches)
            
            # البحث عن أنماط خاصة
            special_patterns = [
                r'["\']([^"\']*m3u8[^"\']*)["\']',
                r'["\']([^"\']*playlist[^"\']*)["\']',
                r'["\']([^"\']*live[^"\']*)["\']'
            ]
            
            for pattern in special_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                found_urls.extend(matches)
    
    # إزالة المكررات وتنظيف الروابط
    found_urls = list(set(found_urls))
    clean_urls = []
    
    for url in found_urls:
        # تنظيف الرابط
        url = url.strip()
        url = re.sub(r'["\'\s<>)]+$', '', url)
        url = re.sub(r'^["\'\s<>)]+', '', url)
        
        # التحقق من صحة الرابط
        if (url.startswith('http') or url.startswith('//')) and len(url) > 10:
            clean_urls.append(url)
    
    # عرض النتائج
    print(f"\n🎯 النتائج:")
    print("=" * 30)
    
    if clean_urls:
        print(f"✅ تم العثور على {len(clean_urls)} رابط بث محتمل:")
        for i, url in enumerate(clean_urls, 1):
            print(f"\n{i}. {url}")
            
            # تحديد نوع الرابط
            if '.m3u8' in url:
                print("   📺 نوع: HLS Stream (m3u8)")
            elif '.ts' in url:
                print("   📺 نوع: Transport Stream (ts)")
            elif '.mpd' in url:
                print("   📺 نوع: DASH Stream (mpd)")
            elif 'playlist' in url.lower():
                print("   📋 نوع: Playlist")
            elif 'live' in url.lower():
                print("   🔴 نوع: Live Stream")
    else:
        print("❌ لم يتم العثور على روابط بث مباشرة")
        
        # عرض النصوص المفكوكة للمراجعة اليدوية
        print("\n📝 النصوص المفكوكة للمراجعة:")
        for i, text in enumerate(decoded_texts[:5], 1):  # أول 5 نصوص
            if len(text) > 20:
                print(f"\n{i}. {text[:200]}...")
    
    # البحث في الكود الأصلي عن أنماط خاصة
    print(f"\n🔍 البحث في الكود الأصلي...")
    
    # أنماط خاصة قد تحتوي على روابط
    special_code_patterns = [
        r'https?://[^\s"\'<>)]+',
        r'[a-zA-Z0-9.-]+\.(?:m3u8|ts|mpd)',
        r'(?:playlist|live|stream)[^\s"\'<>)]*'
    ]
    
    direct_matches = []
    for pattern in special_code_patterns:
        matches = re.findall(pattern, code, re.IGNORECASE)
        direct_matches.extend(matches)
    
    if direct_matches:
        print(f"📋 روابط مباشرة في الكود:")
        for match in set(direct_matches):
            print(f"   - {match}")
    
    # حفظ النتائج
    results = {
        'stream_urls': clean_urls,
        'direct_matches': list(set(direct_matches)),
        'decoded_samples': decoded_texts[:10]  # أول 10 نصوص مفكوكة
    }
    
    with open('stream_urls.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ النتائج في ملف stream_urls.json")
    
    # نصائح للاستخدام
    if clean_urls:
        print(f"\n💡 كيفية استخدام الروابط:")
        print("1. انسخ الرابط")
        print("2. افتح VLC Player")
        print("3. اذهب إلى Media > Open Network Stream")
        print("4. ألصق الرابط واضغط Play")
        print("\nأو استخدم المتصفح مباشرة إذا كان الرابط يدعم ذلك")
    
    return clean_urls

if __name__ == "__main__":
    urls = extract_stream_urls()
    
    if urls:
        print(f"\n🎉 تم العثور على {len(urls)} رابط بث!")
        print("الروابط جاهزة للاستخدام ✅")
    else:
        print(f"\n⚠️ لم يتم العثور على روابط بث مباشرة")
        print("قد تحتاج لتشغيل الكود في متصفح ومراقبة طلبات الشبكة")
