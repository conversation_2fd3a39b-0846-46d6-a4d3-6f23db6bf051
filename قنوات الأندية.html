<!DOCTYPE html>
<head>
  <meta charset="UTF-8">
  <title>قنوات الأندية</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background-image: url('https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?auto=format&fit=crop&w=1050&q=80');
      background-size: cover;
      background-position: center;
      background-attachment: fixed;
      color: #ffffff;
    }

    .overlay {
      background-color: rgba(0, 0, 0, 0.75);
      padding: 20px;
      min-height: 100vh;
    }

    .title {
      text-align: center;
      color: #0EA691;
      font-size: 26px;
      margin-bottom: 10px;
    }

    .search-box {
      text-align: center;
      margin-bottom: 20px;
    }

    .search-input {
      padding: 10px;
      width: 80%;
      max-width: 300px;
      border-radius: 6px;
      border: none;
      outline: none;
    }

    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 12px;
      padding: 10px;
    }

    .card {
      background-color: #2a2a2a;
      border-radius: 10px;
      box-shadow: 0 0 8px #0ea691;
      overflow: hidden;
      text-align: center;
      transition: transform 0.2s;
    }

    .card:hover {
      transform: scale(1.03);
    }

    .channel-img {
      width: 100%;
      height: 100px;
      object-fit: cover;
      border-bottom: 1px solid #0EA691;
    }

    .channel-name {
      padding: 8px;
      font-size: 14px;
      color: #0EA691;
      font-weight: bold;
    }

    .back-btn {
      display: block;
      text-align: center;
      margin-top: 20px;
    }

    .back-btn button {
      padding: 10px 20px;
      border: none;
      background-color: #0EA691;
      color: white;
      border-radius: 6px;
      font-weight: bold;
      cursor: pointer;
    }

    .back-btn button:hover {
      background-color: #088e7a;
    }

    footer {
      text-align: center;
      color: #0EA691;
      margin-top: 40px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="overlay">
    <h2 class="title">⚽ قنوات الأندية</h2>

    <div class="search-box">
      <input type="text" id="search" class="search-input" placeholder="ابحث عن قناة...">
    </div>

    <div class="grid">
      <!-- Al Ahly TV -->
      <a href="go:A7A" class="card">
        <img class="channel-img" src="https://yt3.googleusercontent.com/ytc/AIdro_mGOi86457H-pnYwuUyu2OSWpdvfcmkZBqwJ8Ctfss2lUg=s900-c-k-c0x00ffffff-no-rj">
        <div class="channel-name">Al Ahly TV</div>
      </a>

      <!-- Zamalek TV -->
      <a href="go:ZSC" class="card">
        <img class="channel-img" src="https://upload.wikimedia.org/wikipedia/ar/8/8b/%D9%82%D9%86%D8%A7%D8%A9_%D9%86%D8%A7%D8%AF%D9%8A_%D8%A7%D9%84%D8%B2%D9%85%D8%A7%D9%84%D9%83.jpg">
        <div class="channel-name">Zamalek TV</div>
      </a>

      <!-- Real Madrid TV -->
      <a href="go:realmadrid" class="card">
        <img class="channel-img" src="https://upload.wikimedia.org/wikipedia/en/5/56/Real_Madrid_CF.svg">
        <div class="channel-name">Real Madrid TV</div>
      </a>

      <!-- Barcelona TV -->
      <a href="go:barca" class="card">
        <img class="channel-img" src="https://upload.wikimedia.org/wikipedia/en/4/47/FC_Barcelona_%28crest%29.svg">
        <div class="channel-name">Barça TV</div>
      </a>

      <!-- Liverpool TV -->
      <a href="go:liverpool" class="card">
        <img class="channel-img" src="https://upload.wikimedia.org/wikipedia/en/0/0c/Liverpool_FC.svg">
        <div class="channel-name">Liverpool TV</div>
      </a>

      <!-- Manchester United TV -->
      <a href="go:manutd" class="card">
        <img class="channel-img" src="https://upload.wikimedia.org/wikipedia/en/7/7a/Manchester_United_FC_crest.svg">
        <div class="channel-name">MUTV</div>
      </a>

      <!-- Chelsea TV -->
      <a href="go:chelsea" class="card">
        <img class="channel-img" src="https://upload.wikimedia.org/wikipedia/en/c/cc/Chelsea_FC.svg">
        <div class="channel-name">Chelsea TV</div>
      </a>

      <!-- Arsenal TV -->
      <a href="go:arsenal" class="card">
        <img class="channel-img" src="https://upload.wikimedia.org/wikipedia/en/5/53/Arsenal_FC.svg">
        <div class="channel-name">Arsenal TV</div>
      </a>
    </div>

    <div class="back-btn">
      <button onclick="goHome()">🔙 العودة للصفحة الرئيسية</button>
    </div>

    <footer>
      <p><strong>جميع الحقوق محفوظة © Rayaa Cafe <script>document.write(new Date().getFullYear());</script></strong></p>
    </footer>
  </div>

  <script>
    const searchInput = document.getElementById('search');
    searchInput.addEventListener('input', () => {
      const query = searchInput.value.toLowerCase();
      const cards = document.querySelectorAll('.card');
      cards.forEach(card => {
        const name = card.querySelector('.channel-name').textContent.toLowerCase();
        card.style.display = name.includes(query) ? 'block' : 'none';
      });
    });

    function goHome() {
      window.location.href = "الصفحة الرئيسية.html";
    }
  </script>
</body>
</html>
