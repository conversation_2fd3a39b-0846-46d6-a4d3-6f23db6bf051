<!DOCTYPE html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>جدول مباريات 2025-06-15 - راية كافية</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background-image: url('https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?auto=format&fit=crop&w=1050&q=80');
      background-size: cover;
      background-position: center;
      background-attachment: fixed;
      color: #ffffff;
    }

    .overlay {
      background-color: rgba(0, 0, 0, 0.75);
      padding: 20px;
      min-height: 100vh;
    }

    .title {
      text-align: center;
      color: #0EA691;
      font-size: 28px;
      margin-bottom: 10px;
    }

    .subtitle {
      text-align: center;
      color: #0EA691;
      font-size: 18px;
      margin-bottom: 20px;
      opacity: 0.8;
    }

    .top-buttons {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .top-buttons button {
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      background-color: #0EA691;
      color: #fff;
      font-weight: bold;
      cursor: pointer;
      transition: background 0.3s;
    }

    .top-buttons button:hover {
      background-color: #088e7a;
    }

    .search-box {
      text-align: center;
      margin-bottom: 20px;
    }

    .search-input {
      padding: 10px;
      width: 80%;
      max-width: 300px;
      border-radius: 6px;
      border: none;
      outline: none;
    }

    .section-title {
      color: #0EA691;
      font-size: 20px;
      margin: 30px 10px 15px;
      text-align: center;
    }

    .matches-grid {
      display: flex;
      flex-direction: column;
      gap: 20px;
      padding: 15px;
      max-width: 800px;
      margin: 0 auto;
    }

    .match-card {
      background-color: #2a2a2a;
      border-radius: 20px;
      box-shadow: 0 4px 20px rgba(14, 166, 145, 0.3);
      overflow: hidden;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      position: relative;
    }

    .match-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(14, 166, 145, 0.5);
      border-color: #0EA691;
    }

    .match-card.live {
      border-color: #e74c3c;
      box-shadow: 0 4px 20px rgba(231, 76, 60, 0.4);
    }

    .live-indicator {
      position: absolute;
      top: 15px;
      right: 15px;
      background: #e74c3c;
      color: white;
      padding: 5px 10px;
      border-radius: 15px;
      font-size: 12px;
      font-weight: bold;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.7; }
      100% { opacity: 1; }
    }

    .match-header {
      background: linear-gradient(135deg, #0EA691, #088e7a);
      padding: 20px;
      text-align: center;
      position: relative;
    }

    .match-time {
      font-size: 24px;
      font-weight: bold;
      color: white;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .match-date {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .competition-badge {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      padding: 5px 12px;
      border-radius: 15px;
      font-size: 12px;
      font-weight: bold;
      margin-top: 10px;
      display: inline-block;
    }

    .teams-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 25px 20px;
      background: rgba(255, 255, 255, 0.02);
    }

    .team {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      max-width: 120px;
    }

    .team-logo {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 4px solid #0EA691;
      margin-bottom: 12px;
      background: rgba(255, 255, 255, 0.1);
      transition: transform 0.3s ease;
    }

    .team-logo:hover {
      transform: scale(1.1);
    }

    .no-logo {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(135deg, #0EA691, #088e7a);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      font-weight: bold;
      color: white;
      border: 4px solid #0EA691;
      margin-bottom: 12px;
      transition: transform 0.3s ease;
    }

    .no-logo:hover {
      transform: scale(1.1);
    }

    .team-name {
      font-weight: bold;
      font-size: 16px;
      text-align: center;
      color: #0EA691;
      max-width: 120px;
      word-wrap: break-word;
      line-height: 1.3;
    }

    .vs {
      font-size: 28px;
      font-weight: bold;
      color: #0EA691;
      margin: 0 20px;
      background: rgba(14, 166, 145, 0.1);
      padding: 10px;
      border-radius: 50%;
      min-width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .channel-info {
      background: rgba(14, 166, 145, 0.1);
      padding: 15px;
      border-top: 1px solid #0EA691;
    }

    .original-channel {
      font-size: 12px;
      color: #ccc;
      margin-bottom: 8px;
      text-align: center;
    }

    .app-channel {
      background-color: #0EA691;
      color: white;
      padding: 8px 15px;
      border-radius: 20px;
      font-weight: bold;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: block;
    }

    .app-channel:hover {
      background-color: #088e7a;
      transform: scale(1.05);
    }

    .stats {
      text-align: center;
      margin: 30px 0;
      padding: 20px;
      background: rgba(14, 166, 145, 0.1);
      border-radius: 10px;
      max-width: 600px;
      margin: 30px auto;
    }

    .stats h3 {
      color: #0EA691;
      margin-bottom: 15px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
    }

    .stat-item {
      background: rgba(255, 255, 255, 0.1);
      padding: 15px;
      border-radius: 8px;
      border: 1px solid #0EA691;
    }

    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #0EA691;
    }

    .stat-label {
      font-size: 12px;
      color: #ccc;
      margin-top: 5px;
    }

    footer {
      text-align: center;
      color: #0EA691;
      margin-top: 40px;
      font-size: 14px;
      padding: 20px;
      border-top: 1px solid #0EA691;
    }

    @media (max-width: 768px) {
      .title {
        font-size: 24px;
      }

      .subtitle {
        font-size: 16px;
      }

      .matches-grid {
        padding: 10px;
        gap: 15px;
      }

      .match-card {
        border-radius: 15px;
      }

      .match-header {
        padding: 15px;
      }

      .match-time {
        font-size: 20px;
      }

      .match-date {
        font-size: 14px;
      }

      .teams-container {
        padding: 20px 15px;
        flex-direction: row;
      }

      .team-logo, .no-logo {
        width: 70px;
        height: 70px;
      }

      .team-name {
        font-size: 14px;
        max-width: 100px;
      }

      .vs {
        font-size: 24px;
        margin: 0 10px;
        min-width: 40px;
        height: 40px;
        padding: 8px;
      }

      .channel-info {
        padding: 12px;
      }

      .app-channel {
        padding: 10px 15px;
        font-size: 14px;
      }
    }

    @media (max-width: 480px) {
      .overlay {
        padding: 15px;
      }

      .title {
        font-size: 20px;
      }

      .subtitle {
        font-size: 14px;
      }

      .matches-grid {
        padding: 5px;
        gap: 12px;
      }

      .match-header {
        padding: 12px;
      }

      .match-time {
        font-size: 18px;
      }

      .teams-container {
        padding: 15px 10px;
      }

      .team-logo, .no-logo {
        width: 60px;
        height: 60px;
      }

      .no-logo {
        font-size: 24px;
      }

      .team-name {
        font-size: 13px;
        max-width: 90px;
      }

      .vs {
        font-size: 20px;
        margin: 0 8px;
        min-width: 35px;
        height: 35px;
      }

      .search-input {
        width: 90%;
        padding: 8px;
      }
    }
  </style>
</head>
<body>
  <div class="overlay">
    <h2 class="title">📺 راية كافية</h2>
    <div class="subtitle">جدول مباريات 2025-06-15</div>

    <div class="top-buttons">
      <button onclick="goHome()">🏠 الصفحة الرئيسية</button>
      <button onclick="refreshMatches()">🔄 تحديث</button>
      <button onclick="shareApp()">📤 مشاركة</button>
    </div>

    <div class="search-box">
      <input type="text" id="search" class="search-input" placeholder="ابحث عن مباراة أو فريق...">
    </div>

    <h3 class="section-title">🏆 مباريات اليوم</h3>
    <div class="matches-grid" id="matches">
      <div class="match-card live" data-team1="الأهلي" data-team2="انتر ميامى">
        <div class="live-indicator">🔴 مباشر</div>
        <div class="match-header">
          <div class="match-time">⏰ 03:00</div>
          <div class="match-date">📅 اليوم</div>
          <div class="competition-badge">🏆 كأس العالم للأندية</div>
        </div>

        <div class="teams-container">
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2023\10\19\ahly12023_10_19_17_58.jpg" alt="الأهلي" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">ا</div>
            <div class="team-name">الأهلي المصري</div>
          </div>
          <div class="vs">VS</div>
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2025\5\3\Inter_Miami_CF_logo.svg2025_5_3_16_27.webp" alt="انتر ميامى" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">ا</div>
            <div class="team-name">انتر ميامي</div>
          </div>
        </div>

        <div class="channel-info">
          <div class="original-channel">📺 القناة الأصلية: MBC Masr</div>
          <a href="go:mbcmasr" class="app-channel">
            📱 شاهد مباشرة على MBC Masr
          </a>
        </div>
      </div>
      <div class="match-card" data-team1="بايرن ميونيخ" data-team2="أوكلاند سيتي">
        <div class="match-header">
          <div class="match-time">⏰ 19:00</div>
          <div class="match-date">📅 اليوم</div>
          <div class="competition-badge">🏆 كأس العالم للأندية</div>
        </div>

        <div class="teams-container">
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2018\7\29\BayernMunchen2018_7_29_16_3.jpg" alt="بايرن ميونيخ" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">ب</div>
            <div class="team-name">بايرن ميونيخ</div>
          </div>
          <div class="vs">VS</div>
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2021\12\8\Untitled-12021_12_8_18_51.jpg" alt="أوكلاند سيتي" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">أ</div>
            <div class="team-name">أوكلاند سيتي</div>
          </div>
        </div>

        <div class="channel-info">
          <div class="original-channel">📺 القناة الأصلية: DAZN 1</div>
          <a href="go:dazn1" class="app-channel">
            📱 شاهد على DAZN 1
          </a>
        </div>
      </div>
      <div class="match-card" data-team1="باريس سان جيرمان" data-team2="اتلتيكو مدريد">
        <div class="match-header">
          <div class="match-time">⏰ 22:00</div>
          <div class="match-date">📅 اليوم</div>
          <div class="competition-badge">🏆 كأس العالم للأندية</div>
        </div>

        <div class="teams-container">
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2018\7\29\PSG2018_7_29_17_12.jpg" alt="باريس سان جيرمان" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">ب</div>
            <div class="team-name">باريس سان جيرمان</div>
          </div>
          <div class="vs">VS</div>
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2024\8\11\132024_8_11_20_56.jpg" alt="اتلتيكو مدريد" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">ا</div>
            <div class="team-name">اتلتيكو مدريد</div>
          </div>
        </div>

        <div class="channel-info">
          <div class="original-channel">📺 القناة الأصلية: DAZN 2</div>
          <a href="go:dazn2" class="app-channel">
            📱 شاهد على DAZN 2
          </a>
        </div>
      </div>

      <!-- مباراة إضافية -->
      <div class="match-card" data-team1="ريال مدريد" data-team2="مانشستر سيتي">
        <div class="match-header">
          <div class="match-time">⏰ 20:30</div>
          <div class="match-date">📅 غداً</div>
          <div class="competition-badge">⚽ دوري أبطال أوروبا</div>
        </div>

        <div class="teams-container">
          <div class="team">
            <div class="no-logo">ر</div>
            <div class="team-name">ريال مدريد</div>
          </div>
          <div class="vs">VS</div>
          <div class="team">
            <div class="no-logo">م</div>
            <div class="team-name">مانشستر سيتي</div>
          </div>
        </div>

        <div class="channel-info">
          <div class="original-channel">📺 القناة الأصلية: beIN Sports 1</div>
          <a href="go:bein1" class="app-channel">
            📱 شاهد على beIN Sports 1
          </a>
        </div>
      </div>

      <!-- مباراة إضافية -->
      <div class="match-card" data-team1="الزمالك" data-team2="الأهلي">
        <div class="match-header">
          <div class="match-time">⏰ 18:00</div>
          <div class="match-date">📅 غداً</div>
          <div class="competition-badge">🏆 الدوري المصري</div>
        </div>

        <div class="teams-container">
          <div class="team">
            <div class="no-logo">ز</div>
            <div class="team-name">الزمالك</div>
          </div>
          <div class="vs">VS</div>
          <div class="team">
            <div class="no-logo">ا</div>
            <div class="team-name">الأهلي</div>
          </div>
        </div>

        <div class="channel-info">
          <div class="original-channel">📺 القناة الأصلية: ON Time Sports 1</div>
          <a href="go:on1" class="app-channel">
            📱 شاهد على ON Time Sports 1
          </a>
        </div>
      </div>
    </div>

    <footer>
      <p><strong>جميع الحقوق محفوظة © Rayaa Cafe <script>document.write(new Date().getFullYear());</script></strong></p>
      <p>🔥 تم إنشاؤه بواسطة راية كافية</p>
      <p>⚡ عدد المباريات: 5 | آخر تحديث: <span id="lastUpdate"></span></p>
    </footer>
  </div>

  <script>
    const searchInput = document.getElementById('search');
    searchInput.addEventListener('input', () => {
      const query = searchInput.value.toLowerCase();
      const cards = document.querySelectorAll('.match-card');
      cards.forEach(card => {
        const team1 = card.getAttribute('data-team1');
        const team2 = card.getAttribute('data-team2');
        const isVisible = team1.includes(query) || team2.includes(query);
        card.style.display = isVisible ? 'block' : 'none';
      });
    });

    function downloadPlayer() {
      window.open("https://play.google.com/store/apps/details?id=com.ytv.pronew", "_blank");
    }

    function shareApp() {
      const shareText = "📺 جرّب الآن تطبيق راية كافية!\n\nشاهد مباريات اليوم:\nhttps://your_app_link.com";
      if (navigator.share) {
        navigator.share({
          title: "مباريات اليوم - راية كافية",
          text: shareText,
          url: window.location.href
        });
      } else {
        navigator.clipboard.writeText(shareText).then(() => {
          alert("تم نسخ الرابط! شاركه مع أصدقائك");
        });
      }
    }

    function refreshMatches() {
      window.location.reload();
    }

    function goHome() {
      window.location.href = "الصفحة الرئيسية.html";
    }

    // تأثيرات الحركة وتحديث الوقت
    document.addEventListener('DOMContentLoaded', function() {
      // تحديث الوقت
      const now = new Date();
      const timeString = now.toLocaleTimeString('ar-EG', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
      document.getElementById('lastUpdate').textContent = timeString;

      // تأثيرات الحركة
      const cards = document.querySelectorAll('.match-card');
      cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
          card.style.transition = 'all 0.5s ease';
          card.style.opacity = '1';
          card.style.transform = 'translateY(0)';
        }, index * 150);
      });
    });
  </script>
</body>
</html>