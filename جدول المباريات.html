<!DOCTYPE html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>جدول مباريات 2025-06-15 - راية كافية</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background-image: url('https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?auto=format&fit=crop&w=1050&q=80');
      background-size: cover;
      background-position: center;
      background-attachment: fixed;
      color: #ffffff;
    }

    .overlay {
      background-color: rgba(0, 0, 0, 0.75);
      padding: 20px;
      min-height: 100vh;
    }

    .title {
      text-align: center;
      color: #0EA691;
      font-size: 28px;
      margin-bottom: 10px;
    }

    .subtitle {
      text-align: center;
      color: #0EA691;
      font-size: 18px;
      margin-bottom: 20px;
      opacity: 0.8;
    }

    .top-buttons {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .top-buttons button {
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      background-color: #0EA691;
      color: #fff;
      font-weight: bold;
      cursor: pointer;
      transition: background 0.3s;
    }

    .top-buttons button:hover {
      background-color: #088e7a;
    }

    .search-box {
      text-align: center;
      margin-bottom: 20px;
    }

    .search-input {
      padding: 10px;
      width: 80%;
      max-width: 300px;
      border-radius: 6px;
      border: none;
      outline: none;
    }

    .section-title {
      color: #0EA691;
      font-size: 20px;
      margin: 30px 10px 15px;
      text-align: center;
    }

    .matches-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 15px;
      padding: 10px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .match-card {
      background-color: #2a2a2a;
      border-radius: 15px;
      box-shadow: 0 0 12px #0ea691;
      overflow: hidden;
      transition: transform 0.3s, box-shadow 0.3s;
      border: 2px solid transparent;
    }

    .match-card:hover {
      transform: scale(1.03);
      box-shadow: 0 0 20px #0ea691;
      border-color: #0EA691;
    }

    .match-header {
      background: linear-gradient(135deg, #0EA691, #088e7a);
      padding: 15px;
      text-align: center;
    }

    .match-time {
      font-size: 18px;
      font-weight: bold;
      color: white;
      margin-bottom: 5px;
    }

    .match-date {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
    }

    .teams-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
    }

    .team {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
    }

    .team-logo {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid #0EA691;
      margin-bottom: 10px;
      background: rgba(255, 255, 255, 0.1);
    }

    .no-logo {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #0EA691, #088e7a);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
      color: white;
      border: 3px solid #0EA691;
      margin-bottom: 10px;
    }

    .team-name {
      font-weight: bold;
      font-size: 14px;
      text-align: center;
      color: #0EA691;
      max-width: 100px;
      word-wrap: break-word;
    }

    .vs {
      font-size: 24px;
      font-weight: bold;
      color: #0EA691;
      margin: 0 15px;
    }

    .channel-info {
      background: rgba(14, 166, 145, 0.1);
      padding: 15px;
      border-top: 1px solid #0EA691;
    }

    .original-channel {
      font-size: 12px;
      color: #ccc;
      margin-bottom: 8px;
      text-align: center;
    }

    .app-channel {
      background-color: #0EA691;
      color: white;
      padding: 8px 15px;
      border-radius: 20px;
      font-weight: bold;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: block;
    }

    .app-channel:hover {
      background-color: #088e7a;
      transform: scale(1.05);
    }

    .stats {
      text-align: center;
      margin: 30px 0;
      padding: 20px;
      background: rgba(14, 166, 145, 0.1);
      border-radius: 10px;
      max-width: 600px;
      margin: 30px auto;
    }

    .stats h3 {
      color: #0EA691;
      margin-bottom: 15px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
    }

    .stat-item {
      background: rgba(255, 255, 255, 0.1);
      padding: 15px;
      border-radius: 8px;
      border: 1px solid #0EA691;
    }

    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #0EA691;
    }

    .stat-label {
      font-size: 12px;
      color: #ccc;
      margin-top: 5px;
    }

    footer {
      text-align: center;
      color: #0EA691;
      margin-top: 40px;
      font-size: 14px;
      padding: 20px;
      border-top: 1px solid #0EA691;
    }

    @media (max-width: 768px) {
      .matches-grid {
        grid-template-columns: 1fr;
        padding: 5px;
      }

      .teams-container {
        flex-direction: column;
        gap: 15px;
      }

      .vs {
        transform: rotate(90deg);
        margin: 10px 0;
      }

      .team {
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="overlay">
    <h2 class="title">📺 راية كافية</h2>
    <div class="subtitle">جدول مباريات 2025-06-15</div>

    <div class="search-box">
      <input type="text" id="search" class="search-input" placeholder="ابحث عن مباراة أو فريق...">
    </div>

    <h3 class="section-title">🏆 مباريات اليوم</h3>
    <div class="matches-grid" id="matches">
      <div class="match-card" data-team1="الأهلي" data-team2="انتر ميامى">
        <div class="match-header">
          <div class="match-time">⏰ 03:00</div>
          <div class="match-date">📅 2025-06-15</div>
        </div>

        <div class="teams-container">
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2023\10\19\ahly12023_10_19_17_58.jpg" alt="الأهلي" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">ا</div>
            <div class="team-name">الأهلي</div>
          </div>
          <div class="vs">VS</div>
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2025\5\3\Inter_Miami_CF_logo.svg2025_5_3_16_27.webp" alt="انتر ميامى" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">ا</div>
            <div class="team-name">انتر ميامى</div>
          </div>
        </div>

        <div class="channel-info">
          <div class="original-channel">📺 القناة الأصلية: إم بى س مصر</div>
          <a href="go:mbcmasr" class="app-channel">
            📱 شاهد على إم بى س مصر
          </a>
        </div>
      </div>
      <div class="match-card" data-team1="بايرن ميونيخ" data-team2="أوكلاند سيتي">
        <div class="match-header">
          <div class="match-time">⏰ 19:00</div>
          <div class="match-date">📅 2025-06-15</div>
        </div>

        <div class="teams-container">
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2018\7\29\BayernMunchen2018_7_29_16_3.jpg" alt="بايرن ميونيخ" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">ب</div>
            <div class="team-name">بايرن ميونيخ</div>
          </div>
          <div class="vs">VS</div>
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2021\12\8\Untitled-12021_12_8_18_51.jpg" alt="أوكلاند سيتي" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">أ</div>
            <div class="team-name">أوكلاند سيتي</div>
          </div>
        </div>

        <div class="channel-info">
          <div class="original-channel">📺 القناة الأصلية: DAZN</div>
          <a href="go:dazn1" class="app-channel">
            📱 شاهد على DAZN
          </a>
        </div>
      </div>
      <div class="match-card" data-team1="باريس سان جيرمان" data-team2="اتلتيكو مدريد">
        <div class="match-header">
          <div class="match-time">⏰ 22:00</div>
          <div class="match-date">📅 2025-06-15</div>
        </div>

        <div class="teams-container">
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2018\7\29\PSG2018_7_29_17_12.jpg" alt="باريس سان جيرمان" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">ب</div>
            <div class="team-name">باريس سان جيرمان</div>
          </div>
          <div class="vs">VS</div>
          <div class="team">
            <img src="https://media.gemini.media/img/yallakora/IOSTeams//120/\2024\8\11\132024_8_11_20_56.jpg" alt="اتلتيكو مدريد" class="team-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"><div class="no-logo" style="display:none">ا</div>
            <div class="team-name">اتلتيكو مدريد</div>
          </div>
        </div>

        <div class="channel-info">
          <div class="original-channel">📺 القناة الأصلية:  DAZN</div>
          <a href="go:dazn2" class="app-channel">
            📱 شاهد على DAZN
          </a>
        </div>
      </div>
    </div>

    <footer>
      <p><strong>جميع الحقوق محفوظة © Rayaa Cafe <script>document.write(new Date().getFullYear());</script></strong></p>
      <p>🔥 تم إنشاؤه بواسطة راية كافية</p>
      <p>⚡ عدد المباريات: 3 | آخر تحديث: 23:04</p>
    </footer>
  </div>

  <script>
    const searchInput = document.getElementById('search');
    searchInput.addEventListener('input', () => {
      const query = searchInput.value.toLowerCase();
      const cards = document.querySelectorAll('.match-card');
      cards.forEach(card => {
        const team1 = card.getAttribute('data-team1');
        const team2 = card.getAttribute('data-team2');
        const isVisible = team1.includes(query) || team2.includes(query);
        card.style.display = isVisible ? 'block' : 'none';
      });
    });

    function downloadPlayer() {
      window.open("https://play.google.com/store/apps/details?id=com.ytv.pronew", "_blank");
    }

    function shareApp() {
      const shareText = "📺 جرّب الآن تطبيق راية كافية!\n\nشاهد مباريات اليوم:\nhttps://your_app_link.com";
      if (navigator.share) {
        navigator.share({
          title: "مباريات اليوم - راية كافية",
          text: shareText,
          url: window.location.href
        });
      } else {
        navigator.clipboard.writeText(shareText).then(() => {
          alert("تم نسخ الرابط! شاركه مع أصدقائك");
        });
      }
    }

    function refreshMatches() {
      window.location.reload();
    }

    // تأثيرات الحركة
    document.addEventListener('DOMContentLoaded', function() {
      const cards = document.querySelectorAll('.match-card');
      cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
          card.style.transition = 'all 0.5s ease';
          card.style.opacity = '1';
          card.style.transform = 'translateY(0)';
        }, index * 100);
      });
    });
  </script>
</body>
</html>